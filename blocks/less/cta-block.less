// out: false

@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.ctaBlock {
  &:after {
    height: 50%;
    position: absolute;
    bottom: 0;
    width: 100%;
    left: 0;
    pointer-events: none;
    z-index: 2;
    content: '';
    background: (linear-gradient(rgba(8,0,54,0), @primaryColor));
  }
  svg {
    width: (@vw106 * 6) + (@vw16 * 5);
    height: auto;
    position: relative;
    display: block;
    margin: auto;
    opacity: .3;
    path, rect {
      fill: @hardWhite;
    }
  }
  .light {
    position: absolute;
    left: 50%;
    top: 20%;
    .transform(translate(-50%,-50%));
    pointer-events: none;
    width: 70%;
    height: auto;
    opacity: .4;
    background: radial-gradient(rgba(255,255,255,.37), rgba(255,255,255,0));
    mix-blend-mode: hard-light;
    .filter(blur(40px));
    .rounded(50%);
    .innerLight {
      width: 100%;
      height: 0;
      .paddingRatio(1130,726);
    }
  }
  .iconWrapper {
    pointer-events: none;
    mask-image: linear-gradient(0deg, rgba(0,0,0,.6), rgba(0,0,0,0.9), rgba(0,0,0,0.97));
    -webkit-mask-image: linear-gradient(0deg, rgba(0,0,0,.6), rgba(0,0,0,0.9), rgba(0,0,0,0.97));
  }
  .logoWrapper {
    margin-bottom: @vw55;
    display: block;
    width: 100%;
    text-align: center;
    img {
      display: block;
      margin: auto;
      height: @vw30;
      width: auto;
      object-fit: contain;
    }
  }
  .buttonWrapper {
    margin: auto;
    display: block;
    text-align: center;
    .button {
      &:not(:last-child) {
        margin-right: @vw30;
      }
    }
  }
  .wrapper {
    position: absolute;
    width: auto;
    height: auto;
    top: 50%;
    left: 50%;
    .transform(translate(-50%,-50%));
  }
}

@media all and (max-width: 1080px) {
  .textBlock {
  }
}

@media all and (max-width: 580px) {
  .textBlock {
   
  }
}