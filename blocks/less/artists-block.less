// out: false
.artistsBlock {
  position: relative;
  opacity: 0;
  &.inview {
    opacity: 1;
    .transitionMore(opacity, .6s, .3s);
  }
  &:has(svg) {
        padding-bottom: @vw44;
    }
    svg {
      pointer-events: none;
      position: absolute;
      width: auto;
      height: @vw100 * 6;
      top: 0;
      left: @vw100 * 3;
      opacity: .5;
      z-index: -1;
      path, rect {
        fill: @primaryColor;
      }
    }
   .artists {
    margin-bottom: -@vw16;
    margin-left: -@vw8;
    width: calc(100% ~"+" @vw16);
    .partial {
      margin-bottom: @vw16;
    }
   }
   .filters {
    cursor: pointer;
    margin-bottom: @vw55;
    padding: @vw20 0;
    .rounded(@vw6);
    border: 1px solid @primaryColor;
    width: (@vw106 * 5) + (@vw16 * 4);
    &:has(.filter[data-filter="main"].active) {
      .transform(translateX(0));
    }
    &:has(.filter[data-filter="ones_to_watch"].active) {
      &:before {
        .transform(translateX(100%));
      }
    }
    &:before {
      content: '';
      position: absolute;
      top: @vw5;
      left: @vw5;
      width: calc(50% ~"-" @vw5);
      height: calc(100% ~"-" @vw10);
      background: @primaryColor;
      .rounded(@vw6);
      .transitionMore(transform, .6s, 0s, cubic-bezier(0.85, 0, 0.15, 1));
    }
    .filter {
      cursor: pointer;
      display: inline-block;
      text-align: center;
      width: 50%;
      vertical-align: middle;
      transition: color .3s .3s, opacity .3s;
      -webkit-transition: color .3s .3s, opacity .3s;
      &:hover {
        opacity: .8;
      }
      &.active {
        color: @hardWhite;
        transition: color .3s, opacity .3s;
        -webkit-transition: color .3s, opacity .3s;
      }
    }
    * {
      cursor: pointer;
    }
   }
}

// Media query voor 1080px viewport
@media (max-width: 1080px) {
}

// Media query voor 580px viewport
@media (max-width: 580px) {
  
}
