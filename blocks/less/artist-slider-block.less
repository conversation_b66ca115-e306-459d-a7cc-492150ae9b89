// out: false
.artistSliderBlock {
    &:has(svg) {
        padding-bottom: @vw44;
    }
    svg {
      position: absolute;
      width: auto;
      height: 100%;
      top: 0;
      left: @vw100 * 3;
      opacity: .5;
      path, rect {
        fill: @primaryColor;
      }
    }
  .innerContent {
    width: (@vw112 * 5) + (@vw16 * 4);
    .text {
      margin: @vw60 0;
    }
  }
  .contentWrapper {
    position: relative;
  }
  .sliderWrapper {
    position: relative;
  }
  .topWrapper {
    margin-bottom: @vw55;
    .col {
        display: inline-block;
        vertical-align: middle;
        width: 50%;
        &:last-child {
            text-align: right;
        }
    }
  }
  .arrowButton {
    &:not(:last-child) {
        margin-right: @vw10;
    }
  }
  .sliderIndicator {
    margin-top: @vw50;
    width: (@vw112 * 3) + (@vw16 * 3);
    overflow: hidden;
    height: 2px;
    position: relative;
    background: rgba(255,255,255,.2);
    .innerBar {
      background: @primaryColor;
      border-radius: @vw10;
      height: 100%;
      width: 0;
      position: absolute;
      top: 0;
      left: 0;
    }
  }
  .slide {
    color: @hardWhite;
    display: inline-block;
    cursor: pointer;
    width: calc(25% ~"-" @vw16);
    margin: 0 @vw8;
    will-change: transform;
    overflow: hidden;
    .transform(translate3d(0,0,0));
    .rounded(@vw6);
    height: @vw100 * 5;
    .absoluteContent {
      position: absolute;
      bottom: @vw55;
      left: @vw30;
      z-index: 1;
      .smallTitle, .tinyTitle {
        display: block;
        text-overflow: ellipsis;
        max-width: 100%;
        overflow: hidden;
        white-space: nowrap;
      }
      .smallTitle {
        position: relative;
        left: 0;
        bottom: 0;
      }
      .tinyTitle {
        text-decoration: none;
      }
    }
    &.intro {
        color: @primaryColor;
        border: 1px solid @secondaryColorLight;
        padding: @vw45 @vw25;
        background: transparent;
        &.primary {
            background: @primaryColor;
            color: @hardWhite;
            .text {
                opacity: .8;
            }
        }
        &:not(.primary) {
            &:before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(255,255,255,.1);
                backdrop-filter: blur(6px);
                -webkit-backdrop-filter: blur(6px);
            }
        }
        .tinyTitle {
            margin-bottom: @vw15;
        }
        .button {
            margin-top: @vw20;
            .innerText {
                padding-left: @vw10;
                padding-right: @vw10;
            }
            .innerText {
                padding-left: @vw10;
                padding-right: @vw10;
            }
        }
    }
  }
}

// Media query voor 1080px viewport
@media (max-width: 1080px) {
  .artistSliderBlock {
    .slide {
      width: calc(33.3333% ~"-" @vw16-1080);
      margin: 0 @vw8-1080;
      height: @vw100-1080 * 4.3;
      .rounded(@vw6-1080);
      .absoluteContent {
        bottom: @vw55-1080;
        left: @vw30-1080;
      }
      &.intro {
        padding: @vw45-1080 @vw25-1080;
        .tinyTitle {
          margin-bottom: @vw15-1080;
        }
        .button {
          margin-top: @vw20-1080;
          .innerText {
            padding-left: @vw10-1080;
            padding-right: @vw10-1080;
          }
        }
      }
    }
  }
}

// Media query voor 580px viewport
@media (max-width: 580px) {
  .artistSliderBlock {
    .slide {
      width: calc(50% ~"-" @vw8-580);
      margin: 0 @vw8-580;
      height: @vw100-580 * 5;
      .rounded(@vw6-580);
      .absoluteContent {
        bottom: @vw55-580;
        left: @vw30-580;
      }
      &.intro {
        padding: @vw45-580 @vw25-580;
        .tinyTitle {
          margin-bottom: @vw15-580;
        }
        .button {
          margin-top: @vw20-580;
          .innerText {
            padding-left: @vw10-580;
            padding-right: @vw10-580;
          }
        }
      }
    }
  }
}
