// out: false

@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.introTextBlock {
  .col {
    display: inline-block;
    vertical-align: top;
    width: calc(100% ~"- (" (@vw106 * 2) + (@vw16 * 2) ~")");
    &.small {
      width: (@vw106 * 2) + (@vw16 * 2);
    }
  }
  .introTextWrapper {
    position: relative;
    display: block;
    .mediumTitle {
      &:not(.overlayText) {
        opacity: .4;
      }
      &.overlayText {
        position: absolute;
        pointer-events: none;
        top: 0;
        left: 0;
        .line {
          width: 0%;
          white-space: nowrap;
        }
      }
    }
  }
  .text {
    margin-top: @vw60;
    padding-left: @vw106 + @vw16;
    padding-right: (@vw106 * 3) + (@vw16 * 3);
    .icon {
      width: @vw30;
      display: inline-block;
      vertical-align: top;
      font-size: @vw20;
      line-height: 2;
      .filter(blur(0px));
      .transitionMore(filter,.3s);
      &:hover {
        .filter(blur(10px));
      }
    }
    .innerText {
      display: inline-block;
      vertical-align: top;
      width: calc(100% ~"-" @vw30);
      padding-left: @vw40;
    }
  }
}

@media all and (max-width: 1080px) {
  .textBlock {
  }
}

@media all and (max-width: 580px) {
  .textBlock {
   
  }
}