// out: false
@import '../vw_values.less';
@import '../constants.less';
.partial {
    color: @hardWhite;
    display: inline-block;
    cursor: pointer;
    width: calc(25% ~"-" @vw20);
    margin: 0 @vw8; 
    will-change: transform;
    overflow: hidden;
    .transform(translate3d(0,0,0));
    .rounded(@vw6);
    height: @vw100 * 5;
    &.intro {
        color: @primaryColor;
        border: 1px solid @secondaryColorLight;
        padding: @vw45 @vw25;
        background: transparent;
        &.primary {
            background: @primaryColor;
            color: @hardWhite;
            .text {
                opacity: .8;
            }
        }
        &:not(.primary) {
            &:before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(255,255,255,.1);
                backdrop-filter: blur(6px);
                -webkit-backdrop-filter: blur(6px);
            }
        }
        .tinyTitle {
            margin-bottom: @vw15;
        }
        .button {
            margin-top: @vw20;
            .innerText {
                padding-left: @vw10;
                padding-right: @vw10;
            }
            .innerText {
                padding-left: @vw10;
                padding-right: @vw10;
            }
        }
    }
    &.artist {
        &:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 80%;
            background: linear-gradient(#08003600, @primaryColor);
        }
        &:hover {
            img {
                .transform(scale(1));
            }
            .arrows {
                color: @primaryColor;
                &:before {
                    .transform(translate(-50%,-50%) scale(1.06));
                }
                i {
                    &:first-child {
                    .transform(translate(-50%, -50%));
                    }
                    &:last-child {
                    .transform(translate(100%, -200%) scale(.5));
                    }
                }
            }
        }
        img {
            position: absolute;
            top: 0;
            left: 0;
            pointer-events: none;
            width: 100%;
            object-fit: cover;
            object-position: top;
            height: 100%;
            .transform(scale(1.05));
            .transitionMore(transform, 0.6s, 0s, cubic-bezier(0.22, 1, 0.36, 1));
        }
        .smallTitle {
            position: absolute;
            bottom: @vw55;
            left: @vw30;
            z-index: 1;
        }
        .arrows {
            display: inline-block;
            vertical-align: middle;
            background: @primaryColor;
            width: @vw55;
            height: @vw55;
            font-size: @vw20;
            .rounded(@vw6);
            line-height: @vw34;
            text-align: center;
            z-index: 2;
            position: relative;
            color: @hardWhite;
            overflow: hidden;
            position: absolute;
            top: @vw10;
            right: @vw10;
            .transitionMore(border-color,.3s);
            &:before {
                content: '';
                background: @hardWhite;
                position: absolute;
                top: 50%;
                left: 50%;
                width: 100%;
                height: 100%;
                .rounded(@vw6);
                .transform(translate(-50%,-50%) scale(0));
                .transitionMore(transform, .45s, 0s, cubic-bezier(0.34, 1.56, 0.64, 1));
            }
            i {
                position: absolute;
                left: 50%;
                top: 50%;
                .transform(translate(-50%, -50%));
                .transitionMore(transform, .6s, 0s, cubic-bezier(0.85, 0, 0.15, 1));
                &:first-child {
                    .transform(translate(-200%, 100%) scale(.5));
                }
            }
        }
    }
}

@media all and (max-width: 1080px) {
    .partial {
        width: calc(33.3333% ~"-" @vw16-1080);
        margin: 0 @vw8-1080;
        height: @vw100-1080 * 5;
        .rounded(@vw6-1080);
        &.intro {
            padding: @vw45-1080 @vw25-1080;
            .tinyTitle {
                margin-bottom: @vw15-1080;
            }
            .button {
                margin-top: @vw20-1080;
                .innerText {
                    padding-left: @vw10-1080;
                    padding-right: @vw10-1080;
                }
            }
        }
        &.artist {
            img {
                height: @vw100-1080 * 5;
            }
            .smallTitle {
                bottom: @vw55-1080;
            }
            .arrows {
                width: @vw55-1080;
                height: @vw55-1080;
                font-size: @vw20-1080;
                line-height: @vw34-1080;
                top: @vw10-1080;
                right: @vw10-1080;
            }
        }
    }
}

@media all and (max-width: 580px) {
    .partial {
        width: calc(50% ~"-" @vw8-580);
        margin: 0 @vw8-580;
        height: @vw100-580 * 4.3;
        .rounded(@vw6-580);
        &.intro {
            padding: @vw45-580 @vw25-580;
            .tinyTitle {
                margin-bottom: @vw15-580;
            }
            .button {
                margin-top: @vw20-580;
                .innerText {
                    padding-left: @vw10-580;
                    padding-right: @vw10-580;
                }
            }
        }
        &.artist {
            img {
                height: @vw100-580 * 5;
            }
            .smallTitle {
                bottom: @vw55-580;
            }
            .arrows {
                width: @vw55-580;
                height: @vw55-580;
                font-size: @vw20-580;
                line-height: @vw34-580;
                top: @vw10-580;
                right: @vw10-580;
            }
        }
    }
}