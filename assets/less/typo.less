@import 'vw_values.less';
@import 'constants.less'; 

.hugeTitle, .bigTitle, .biggerTitle, .normalTitle, .smallTitle, .subTitle, .tinyTitle {
  &.white {
    color: @hardWhite;
  }
  .white {
    color: @hardWhite;
  }
}

.hugeTitle {
  font-size: @vw100;
  text-decoration: none;
  font-family: 'ApexMk2-Regular', Arial, sans-serif;
  font-weight: normal;
  font-style: normal;
  .transitionMore(opacity, .3s);
  &.link {
    cursor: pointer;
    color: @primaryColor;
    &:hover {
      opacity: .6;
    }
    span {
      cursor: pointer;
    }
  }
}

.bigTitle {
  font-size: @vw100 + @vw8;
  letter-spacing: -6px;
  font-family: 'ApexMk2-Regular', Arial, sans-serif;
  font-weight: normal;
  font-style: normal;
  &.compact {
    font-size: @vw100 + @vw30;
    text-transform: uppercase;
    letter-spacing: 0;
    font-family: 'ApexMk2-Regular', <PERSON><PERSON>, sans-serif;
    font-weight: normal;
    font-style: normal;
  }
}

.mediumTitle {
  font-size: @vw60;
  letter-spacing: 0;
  font-family: 'ApexMk2-Regular', Arial, sans-serif;
  font-weight: normal;
  font-style: normal;
  line-height: 1.15;
}

.normalTitle {
  font-size: @vw40;
  letter-spacing: 0;
  font-family: 'ApexMk2-Regular', Arial, sans-serif;
  font-weight: normal;
  font-style: normal;
  line-height: 1.15;
}

.smallTitle {
  font-size: @vw30;
  letter-spacing: 0;
  font-family: 'ApexMk2-Regular', Arial, sans-serif;
  font-weight: normal;
  font-style: normal;
  line-height: 1.15;
}

.subTitle {
  font-size: @vw24;
  line-height: 1.4;
  font-family: 'ApexMk2-Regular', Arial, sans-serif;
  font-weight: 500;
  font-style: normal;
  &.primary {
    color: @primaryColor;
  }
  &.secondary {
    color: @secondaryColor;
  }
}

.tinyTitle {
  font-size: @vw20;
  line-height: 1.4;
  text-transform: uppercase;
  font-family: 'ApexMk2-Regular', Arial, sans-serif;
  font-weight: normal;
  font-style: normal;
  &.smaller {
    font-size: @vw17;
  }
}

.text {
  &.bigger {
    font-size: @vw22;
    text-transform: uppercase;
    p {
      font-size: @vw22;
      text-transform: uppercase;
    }
  }
  &.white {
    p {
      color: @hardWhite;
    }
  }
  &:not(:first-child) {
    margin-top: @vw20;
  }
  p {
    &:not(:last-child) {
      margin-bottom: @vw30;
    }
  }
}

@media all and (max-width: 1080px) {
  .hugeTitle {
    font-size: @vw60-1080;
  }

  .bigTitle {
    font-size: @vw60-1080;
    letter-spacing: -2px;
    &.compact {
      font-size: @vw60-1080;
    }
  }

  .mediumTitle {
    font-size: @vw40-1080;
  }
  .normalTitle {
    font-size: @vw30-1080;
  }

  .subTitle {
    font-size: @vw24-1080;
  }

  .tinyTitle {
    font-size: @vw16-1080;
    &.smaller {
      font-size: @vw16-1080;
    }
  }

  .text {
    &.bigger {
      font-size: @vw22-1080;
      p {
        font-size: @vw22-1080;
      }
    }
    &:not(:first-child) {
      margin-top: @vw20-1080;
    }
    p {
      &:not(:last-child) {
        margin-bottom: @vw22-1080;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .hugeTitle {
    font-size: @vw35-580;
  }

  .bigTitle {
    font-size: @vw70-580;
    &.compact {
      font-size: @vw70-580;
    }
  }

  .mediumTitle {
    font-size: @vw50-580;
  }

  .subTitle {
    font-size: @vw24-580;
  }

  .tinyTitle {
    font-size: @vw16-580;
  }

  .text {
    &.bigger {
      font-size: @vw22-580;
      p { 
        font-size: @vw22-580;
      }
    }
    &:not(:first-child) {
      margin-top: @vw20-580;
    }
    p {
      &:not(:last-child) {
        margin-bottom: @vw22-580;
      }
    }
  }
}
